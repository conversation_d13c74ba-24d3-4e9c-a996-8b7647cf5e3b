/*
 * This file is part of Part-DB (https://github.com/Part-DB/Part-DB-symfony).
 *
 *  Copyright (C) 2019 - 2022 <PERSON> (https://github.com/jbtronics)
 *
 *  This program is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Affero General Public License as published
 *  by the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Affero General Public License for more details.
 *
 *  You should have received a copy of the GNU Affero General Public License
 *  along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

.cke_placeholder {
    background: #ffff00;
    padding: 1px 2px;
    outline-offset: -2px;
    line-height: 1em;
    margin: 0 1px;
    display: inline-block;
}

.cke_placeholder::selection {
    display: none;
}

/*.cke_overflow_dropdown {
    overflow: auto;
    height: 40vh;
}*/

/** Make long editor dropdown panels scrollable */
.ck-dropdown__panel {
    overflow: auto;
    max-height: 40vh;
}
