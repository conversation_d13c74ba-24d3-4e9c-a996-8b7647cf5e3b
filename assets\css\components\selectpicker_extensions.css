/*
 * This file is part of Part-DB (https://github.com/Part-DB/Part-DB-symfony).
 *
 *  Copyright (C) 2019 - 2022 Jan <PERSON> (https://github.com/jbtronics)
 *
 *  This program is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Affero General Public License as published
 *  by the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Affero General Public License for more details.
 *
 *  You should have received a copy of the GNU Affero General Public License
 *  along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

/***********************************************
 * Special level whitespace characters that only show up when inside a bootstrap-select dropdown
 ***********************************************/
.dropdown-item span.picker-level::after {
    content: "\00a0\00a0\00a0"; /* 3 spaces */
}

/** Bootstrap-select Hide on Selected element */
.picker-hs {
    display: none;
}
.dropdown-item .picker-hs {
    display: inherit;
}

/** Bootstrap-select Unhide on selected element */
.picker-us {
    display: inherit;
}
.dropdown-item .picker-us {
    display: none;
}
