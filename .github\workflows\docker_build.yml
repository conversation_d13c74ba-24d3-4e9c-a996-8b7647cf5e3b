name: Docker Image Build

on:
  #schedule:
  #  - cron: '0 10 * * *' # everyday at 10am
  push:
    branches:
      - '**'
      - '!l10n_**'
    tags:
      - 'v*.*.*'
      - 'v*.*.*-**'

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4
      -
        name: Docker meta
        id: docker_meta
        uses: docker/metadata-action@v5
        with:
          # list of Docker images to use as base name for tags
          images: |
            jbtronics/part-db1
          # Mark the image build from master as latest (as we dont have really releases yet)
          tags: |
            type=edge,branch=master
            type=ref,event=branch,
            type=ref,event=tag,
            type=schedule
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=ref,event=branch
            type=ref,event=pr
          labels: |
            org.opencontainers.image.source=${{ github.event.repository.clone_url }}
            org.opencontainers.image.revision=${{ github.sha }}
            org.opencontainers.image.title=Part-DB
            org.opencontainers.image.description=Part-DB is a web application for managing electronic components and your inventory.
            org.opencontainers.image.url=https://github.com/Part-DB/Part-DB-symfony
            org.opencontainers.image.source=https://github.com/Part-DB/Part-DB-symfony
            org.opencontainers.image.authors=Jan Böhmer
            org.opencontainers.licenses=AGPL-3.0-or-later

      -
        name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: 'arm64,arm'
      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      -
        name: Login to DockerHub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      -
        name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64,linux/arm/v7
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.docker_meta.outputs.tags }}
          labels: ${{ steps.docker_meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max