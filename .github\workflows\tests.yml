name: PHPUnit Tests

on:
  push:
    branches:
      - '*'
      - "!l10n_*" # Dont test localization branches
  pull_request:
    branches:
      - '*'
      - "!l10n_*"
    
jobs:
  phpunit:
    name: PHPUnit and coverage Test (PHP ${{ matrix.php-versions }}, ${{ matrix.db-type }})
    runs-on: ubuntu-22.04

    strategy:
      fail-fast: false
      matrix:
        php-versions: [ '8.1', '8.2', '8.3', '8.4' ]
        db-type: [ 'mysql', 'sqlite', 'postgres' ]

    env:
      # Note that we set DATABASE URL later based on our db-type matrix value
      APP_ENV: test
      SYMFONY_DEPRECATIONS_HELPER: disabled
      PHP_VERSION: ${{ matrix.php-versions }}
      DB_TYPE: ${{ matrix.db-type }}
      CHECK_FOR_UPDATES: false  # Disable update checks for tests

    steps:
      - name: Set Database env for MySQL
        run: echo "DATABASE_URL=mysql://root:root@127.0.0.1:3306/partdb?serverVersion=8.0.35" >> $GITHUB_ENV
        if: matrix.db-type == 'mysql'

      - name: Set Database env for SQLite
        run: echo "DATABASE_URL="sqlite:///%kernel.project_dir%/var/app_test.db"" >> $GITHUB_ENV
        if: matrix.db-type == 'sqlite'

      - name: Set Database env for PostgreSQL
        run: echo "DATABASE_URL=postgresql://postgres:postgres @127.0.0.1:5432/partdb?serverVersion=14&charset=utf8" >> $GITHUB_ENV
        if: matrix.db-type == 'postgres'

      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          coverage: pcov
          ini-values: xdebug.max_nesting_level=1000
          extensions: mbstring, intl, gd, xsl, gmp, bcmath, :php-psr
    
      - name: Start MySQL
        run: sudo systemctl start mysql.service
        if: matrix.db-type == 'mysql'

        # Replace the scram-sha-256 with trust for host connections, to avoid password authentication
      - name: Start PostgreSQL
        run: |
          sudo sed -i 's/^\(host.*all.*all.*\)scram-sha-256/\1trust/' /etc/postgresql/14/main/pg_hba.conf
          sudo systemctl start postgresql.service
          sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'postgres';"
        if: matrix.db-type == 'postgres'

        #- name: Setup MySQL
      #  uses: mirromutth/mysql-action@v1.1
      #  with:
      #    mysql version: 5.7
      #    mysql database: 'part-db'
      #    mysql root password: '1234'
        
      ## Setup caches
      
      - name: Get Composer Cache Directory
        id: composer-cache
        run: |
          echo "::set-output name=dir::$(composer config cache-files-dir)"
      - uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-composer-  
      
      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v4
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-
      
      - name: Install composer dependencies
        run: composer install --prefer-dist --no-progress
    
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: '18'
    
      - name: Install yarn dependencies
        run: yarn install
    
      - name: Build frontend
        run: yarn build
    
      - name: Create DB
        run: php bin/console --env test doctrine:database:create --if-not-exists -n
        if: matrix.db-type == 'mysql' || matrix.db-type == 'postgres'
    
      - name: Do migrations
        run: php bin/console --env test doctrine:migrations:migrate -n

      # Use our own custom fixtures loading command to circumvent some problems with reset the autoincrement values
      - name: Load fixtures
        run: php bin/console --env test partdb:fixtures:load -n
    
      - name: Run PHPunit and generate coverage
        run: ./bin/phpunit --coverage-clover=coverage.xml
      
      - name: Upload coverage
        uses: codecov/codecov-action@v5
        with:
          env_vars: PHP_VERSION,DB_TYPE
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: true
          
      - name: Test app:clean-attachments
        run: php bin/console partdb:attachments:clean-unused -n
      
      - name: Test app:convert-bbcode
        run: php bin/console app:convert-bbcode -n
      
      - name: Test app:show-logs
        run: php bin/console app:show-logs -n

      - name: Test check-requirements command
        run: php bin/console partdb:check-requirements -n

      - name: Test partdb:backup command
        run: php bin/console partdb:backup -n --full /tmp/test_backup.zip

      - name: Test legacy Part-DB import
        run: bash .github/assets/legacy_import/test_legacy_import.sh
        if: matrix.db-type == 'mysql' && matrix.php-versions == '8.2'
        env:
          DATABASE_URL: mysql://root:root@localhost:3306/legacy_db
