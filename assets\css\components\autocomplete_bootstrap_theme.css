/*
 * This file is part of Part-DB (https://github.com/Part-DB/Part-DB-symfony).
 *
 *  Copyright (C) 2019 - 2024 Jan <PERSON> (https://github.com/jbtronics)
 *
 *  This program is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Affero General Public License as published
 *  by the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Affero General Public License for more details.
 *
 *  You should have received a copy of the GNU Affero General Public License
 *  along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

/***
 * This file is based on the autocomplete-theme-classic from Algolia and modifies it to fit better into the bootstrap 5
 * theme of Part-DB.
 */

/*! @algolia/autocomplete-theme-classic 1.17.0 | MIT License | © Algolia, Inc. and contributors | https://github.com/algolia/autocomplete */
/* ----------------*/
/* 1.  CSS Variables*/
/* 2.  Dark Mode*/
/* 3.  Autocomplete*/
/* 4.  Panel*/
/* 5.  Sources*/
/* 6.  Hit Layout*/
/* 7.  Panel Header*/
/* 8.  Panel Footer*/
/* 9.  Detached Mode*/
/* 10. Gradients*/
/* 11. Utilities*/
/* ----------------*/
/* Note:*/
/* This theme reflects the markup structure of autocomplete with SCSS indentation.*/
/* We use the SASS `@at-root` function to keep specificity low.*/
/* ----------------*/
/* 1. CSS Variables*/
/* ----------------*/
:root {
    /* Input*/
    --aa-search-input-height: 44px;
    --aa-input-icon-size: 20px;
    /* Size and spacing*/
    --aa-base-unit: 16;
    --aa-spacing-factor: 1;
    --aa-spacing: calc(var(--aa-base-unit) * var(--aa-spacing-factor) * 1px);
    --aa-spacing-half: calc(var(--aa-spacing) / 2);
    --aa-panel-max-height: 650px;
    /* Z-index*/
    --aa-base-z-index: 9999;
    /* Font*/
    --aa-font-size: calc(var(--aa-base-unit) * 1px);
    --aa-font-family: inherit;
    --aa-font-weight-medium: 500;
    --aa-font-weight-semibold: 600;
    --aa-font-weight-bold: 700;
    /* Icons*/
    --aa-icon-size: 20px;
    --aa-icon-stroke-width: 1.6;
    --aa-icon-color-rgb: 119, 119, 163;
    --aa-icon-color-alpha: 1;
    --aa-action-icon-size: 20px;
    /* Text colors*/
    --aa-text-color-rgb: 38, 38, 39;
    --aa-text-color-alpha: 1;
    --aa-primary-color-rgb: 62, 52, 211;
    --aa-primary-color-alpha: 0.2;
    --aa-muted-color-rgb: 128, 126, 163;
    --aa-muted-color-alpha: 0.6;
    /* Border colors*/
    --aa-panel-border-color-rgb: 128, 126, 163;
    --aa-panel-border-color-alpha: 0.3;
    --aa-input-border-color-rgb: 128, 126, 163;
    --aa-input-border-color-alpha: 0.8;
    /* Background colors*/
    --aa-background-color-rgb: 255, 255, 255;
    --aa-background-color-alpha: 1;
    --aa-input-background-color-rgb: 255, 255, 255;
    --aa-input-background-color-alpha: 1;
    --aa-selected-color-rgb: 179, 173, 214;
    --aa-selected-color-alpha: 0.205;
    --aa-description-highlight-background-color-rgb: 245, 223, 77;
    --aa-description-highlight-background-color-alpha: 0.5;
    /* Detached mode*/
    --aa-detached-media-query: (max-width: 680px);
    --aa-detached-modal-media-query: (min-width: 680px);
    --aa-detached-modal-max-width: 680px;
    --aa-detached-modal-max-height: 500px;
    --aa-overlay-color-rgb: 115, 114, 129;
    --aa-overlay-color-alpha: 0.4;
    /* Shadows*/
    --aa-panel-shadow: 0 0 0 1px rgba(35, 38, 59, .1),
    0 6px 16px -4px rgba(35, 38, 59, .15);
    /* Scrollbar*/
    --aa-scrollbar-width: 13px;
    --aa-scrollbar-track-background-color-rgb: 234, 234, 234;
    --aa-scrollbar-track-background-color-alpha: 1;
    --aa-scrollbar-thumb-background-color-rgb: var(--aa-background-color-rgb);
    --aa-scrollbar-thumb-background-color-alpha: 1;
    /* Touch screens*/
}
@media (hover: none) and (pointer: coarse) {
    :root {
        --aa-spacing-factor: 1.2;
        --aa-action-icon-size: 22px;
    }
}

/* ----------------*/
/* 2. Dark Mode*/
/* ----------------*/
body {
    /* stylelint-disable selector-no-qualifying-type, selector-class-pattern */
    /* stylelint-enable selector-no-qualifying-type, selector-class-pattern */
}

/* Reset for `@extend`*/
.aa-Panel *, .aa-Autocomplete *,
.aa-DetachedFormContainer * {
    box-sizing: border-box;
}

/* Init for `@extend`*/
.aa-Panel, .aa-Autocomplete,
.aa-DetachedFormContainer {
    color: rgba(var(--aa-text-color-rgb), var(--aa-text-color-alpha));
    color: var(--bs-body-color);
    font-family: inherit;
    font-weight: normal;
    line-height: 1em;
    margin: 0;
    padding: 0;
    text-align: left;
}

/* ----------------*/
/* 3. Autocomplete*/
/* ----------------*/
.aa-Autocomplete,
.aa-DetachedFormContainer {
    /* Search box*/
}
.aa-Form {
    align-items: center;
    background-color: var(--bs-body-bg);
    border: var(--bs-border-width) solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    color: var(--bs-body-color);
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    display: flex;
    line-height: 1em;
    margin: 0;
    position: relative;
    width: 100%;
}
.aa-Form:focus-within {
    background-color: var(--bs-body-bg);
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13,110,253,.25);
    color: var(--bs-body-color);
    outline: 0;
}
.aa-InputWrapperPrefix {
    align-items: center;
    display: flex;
    flex-shrink: 0;
    height: 44px;
    height: var(--aa-search-input-height);
    order: 1;
    /* Container for search and loading icons*/
}
.aa-Label,
.aa-LoadingIndicator {
    cursor: auto;
    cursor: initial;
    flex-shrink: 0;
    height: 100%;
    padding: 0;
    text-align: left;
}
.aa-Label svg,
.aa-LoadingIndicator svg {
    color: rgba(var(--bs-primary-rgb), 1.0);
    height: auto;
    max-height: 20px;
    max-height: var(--aa-input-icon-size);
    stroke-width: 1.6;
    stroke-width: var(--aa-icon-stroke-width);
    width: 20px;
    width: var(--aa-input-icon-size);
}

.aa-SubmitButton,
.aa-LoadingIndicator {
    height: 100%;
    padding-left: calc((16 * 1 * 1px) * 0.75 - 1px);
    padding-left: calc(calc(16 * 1 * 1px) * 0.75 - 1px);
    padding-left: calc(var(--aa-spacing) * 0.75 - 1px);
    padding-right: calc((16 * 1 * 1px) / 2);
    padding-right: calc(calc(16 * 1 * 1px) / 2);
    padding-right: var(--aa-spacing-half);
    width: calc((16 * 1 * 1px) * 1.75 + 20px - 1px);
    width: calc(calc(16 * 1 * 1px) * 1.75 + 20px - 1px);
    width: calc(var(--aa-spacing) * 1.75 + var(--aa-icon-size) - 1px);
}
@media (hover: none) and (pointer: coarse) {
    .aa-SubmitButton,
    .aa-LoadingIndicator {
        padding-left: calc(((16 * 1 * 1px) / 2) / 2 - 1px);
        padding-left: calc(calc(calc(16 * 1 * 1px) / 2) / 2 - 1px);
        padding-left: calc(var(--aa-spacing-half) / 2 - 1px);
        width: calc(20px + (16 * 1 * 1px) * 1.25 - 1px);
        width: calc(20px + calc(16 * 1 * 1px) * 1.25 - 1px);
        width: calc(var(--aa-icon-size) + var(--aa-spacing) * 1.25 - 1px);
    }
}

.aa-SubmitButton {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: none;
    border: 0;
    margin: 0;
}

.aa-LoadingIndicator {
    align-items: center;
    display: flex;
    justify-content: center;
}
.aa-LoadingIndicator[hidden] {
    display: none;
}

.aa-InputWrapper {
    order: 3;
    position: relative;
    width: 100%;
    /* Search box input (with placeholder and query)*/
}
.aa-Input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: none;
    border: 0;
    color: var(--bs-body-color);
    font: inherit;
    height: 44px;
    height: var(--aa-search-input-height);
    padding: 0;
    width: 100%;
    /* Focus is set and styled on the parent, it isn't necessary here*/
    /* Remove native appearance*/
}
.aa-Input::-moz-placeholder {
    color: var(--bs-secondary-color);
    opacity: 1;
}
.aa-Input::placeholder {
    color: var(--bs-secondary-color);
    opacity: 1;
}
.aa-Input:focus {
    box-shadow: none;
    outline: none;
}
.aa-Input::-webkit-search-decoration, .aa-Input::-webkit-search-cancel-button, .aa-Input::-webkit-search-results-button, .aa-Input::-webkit-search-results-decoration {
    -webkit-appearance: none;
    appearance: none;
}

.aa-InputWrapperSuffix {
    align-items: center;
    display: flex;
    height: 44px;
    height: var(--aa-search-input-height);
    order: 4;
    /* Accelerator to clear the query*/
}
.aa-ClearButton {
    align-items: center;
    background: none;
    border: 0;
    color: var(--bs-secondary-color);
    cursor: pointer;
    display: flex;
    height: 100%;
    margin: 0;
    padding: 0 calc((16 * 1 * 1px) * 0.8333333333 - 0.5px);
    padding: 0 calc(calc(16 * 1 * 1px) * 0.8333333333 - 0.5px);
    padding: 0 calc(var(--aa-spacing) * 0.8333333333 - 0.5px);
}
@media (hover: none) and (pointer: coarse) {
    .aa-ClearButton {
        padding: 0 calc((16 * 1 * 1px) * 0.6666666667 - 0.5px);
        padding: 0 calc(calc(16 * 1 * 1px) * 0.6666666667 - 0.5px);
        padding: 0 calc(var(--aa-spacing) * 0.6666666667 - 0.5px);
    }
}
.aa-ClearButton:hover, .aa-ClearButton:focus {
    color: var(--bs-body-color);
}
.aa-ClearButton[hidden] {
    display: none;
}
.aa-ClearButton svg {
    stroke-width: 1.6;
    stroke-width: var(--aa-icon-stroke-width);
    width: 20px;
    width: var(--aa-icon-size);
}

/* ----------------*/
/* 4. Panel*/
/* ----------------*/
.aa-Panel {
    --bs-dropdown-header-padding-x: 1rem;
    --bs-dropdown-header-padding-y: 0.5rem;
    --bs-dropdown-font-size: 1rem;
    --bs-dropdown-color: var(--bs-body-color);
    --bs-dropdown-bg: var(--bs-body-bg);
    --bs-dropdown-border-color: var(--bs-border-color-translucent);
    --bs-dropdown-border-radius: var(--bs-border-radius);
    --bs-dropdown-border-width: var(--bs-border-width);

    z-index: 1000;

    box-shadow: 0 0 0 1px rgba(35, 38, 59, 0.1);
    overflow: hidden;
    position: absolute;
    transition: opacity 200ms ease-in, filter 200ms ease-in;
    /* When a request isn't resolved yet*/

    padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
    margin: 0;
    font-size: var(--bs-dropdown-font-size);
    color: var(--bs-dropdown-color);
    background-color: var(--bs-dropdown-bg);
    background-clip: padding-box;
    border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
    border-radius: var(--bs-dropdown-border-radius);
}
@media screen and (prefers-reduced-motion) {
    .aa-Panel {
        transition: none;
    }
}
.aa-Panel button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: none;
    border: 0;
    margin: 0;
    padding: 0;
}
.aa-PanelLayout {
    height: 100%;
    margin: 0;
    max-height: 650px;
    max-height: var(--aa-panel-max-height);
    overflow-y: auto;
    padding: 0;
    position: relative;
    text-align: left;
}
.aa-PanelLayoutColumns--twoGolden {
    display: grid;
    grid-template-columns: 39.2% auto;
    overflow: hidden;
    padding: 0;
}

.aa-PanelLayoutColumns--two {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    overflow: hidden;
    padding: 0;
}

.aa-PanelLayoutColumns--three {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    overflow: hidden;
    padding: 0;
}

.aa-Panel--stalled .aa-Source {
    filter: grayscale(1);
    opacity: 0.8;
}

.aa-Panel--scrollable {
    margin: 0;
    max-height: 650px;
    max-height: var(--aa-panel-max-height);
    overflow-x: hidden;
    overflow-y: auto;
    padding: calc((16 * 1 * 1px) / 2);
    padding: calc(calc(16 * 1 * 1px) / 2);
    padding: var(--aa-spacing-half);
    scrollbar-color: rgba(255, 255, 255, 1) rgba(234, 234, 234, 1);
    scrollbar-color: rgba(var(--aa-scrollbar-thumb-background-color-rgb), var(--aa-scrollbar-thumb-background-color-alpha)) rgba(var(--aa-scrollbar-track-background-color-rgb), var(--aa-scrollbar-track-background-color-alpha));
    scrollbar-width: thin;
}
.aa-Panel--scrollable::-webkit-scrollbar {
    width: 13px;
    width: var(--aa-scrollbar-width);
}
.aa-Panel--scrollable::-webkit-scrollbar-track {
    background-color: rgba(234, 234, 234, 1);
    background-color: rgba(var(--aa-scrollbar-track-background-color-rgb), var(--aa-scrollbar-track-background-color-alpha));
}
.aa-Panel--scrollable::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 1);
    background-color: rgba(var(--aa-scrollbar-thumb-background-color-rgb), var(--aa-scrollbar-thumb-background-color-alpha));
    border-color: rgba(234, 234, 234, 1);
    border-color: rgba(var(--aa-scrollbar-track-background-color-rgb), var(--aa-scrollbar-track-background-color-alpha));
    border-radius: 9999px;
    border-style: solid;
    border-width: 3px 2px 3px 3px;
}

/* ----------------*/
/* 5. Sources*/
/* Each source can be styled independently*/
/* ----------------*/
.aa-Source {
    margin: 0;
    padding: 0;
    position: relative;
    width: 100%;
    /* List of results inside the source*/
    /* Source title*/
    /* See all button*/
}
.aa-Source:empty {
    /* Hide empty section*/
    display: none;
}
.aa-SourceNoResults {
    font-size: 1em;
    margin: 0;
    padding: calc(16 * 1 * 1px);
    padding: var(--aa-spacing);
}

.aa-List {
    list-style: none;
    margin: 0;
    padding: 0;
    position: relative;
}

.aa-SourceHeader {
    margin: calc((16 * 1 * 1px) / 2) 0.5em calc((16 * 1 * 1px) / 2) 0;
    margin: calc(calc(16 * 1 * 1px) / 2) 0.5em calc(calc(16 * 1 * 1px) / 2) 0;
    margin: var(--aa-spacing-half) 0.5em var(--aa-spacing-half) 0;
    padding: 0;
    position: relative;
    /* Hide empty header*/
    /* Title typography*/
    /* Line separator*/
}
.aa-SourceHeader:empty {
    display: none;
}
.aa-SourceHeaderTitle {
    background: var(--bs-body-bg);
    color: rgba(var(--bs-primary-rgb), 1.0);
    display: inline-block;
    font-size: 0.8em;
    font-weight: 600;
    font-weight: var(--aa-font-weight-semibold);
    margin: 0;
    padding: 0 calc((16 * 1 * 1px) / 2) 0 0;
    padding: 0 calc(calc(16 * 1 * 1px) / 2) 0 0;
    padding: 0 var(--aa-spacing-half) 0 0;
    position: relative;
    z-index: 9999;
    z-index: var(--aa-base-z-index);
}

.aa-SourceHeaderLine {
    border-bottom: solid 1px rgba(var(--bs-primary-rgb), 1.0);
    display: block;
    height: 2px;
    left: 0;
    margin: 0;
    opacity: 0.3;
    padding: 0;
    position: absolute;
    right: 0;
    top: calc((16 * 1 * 1px) / 2);
    top: calc(calc(16 * 1 * 1px) / 2);
    top: var(--aa-spacing-half);
    z-index: calc(9999 - 1);
    z-index: calc(var(--aa-base-z-index) - 1);
}

.aa-SourceFooterSeeAll {
    background: linear-gradient(180deg, var(--bs-body-bg), rgba(128, 126, 163, 0.14));
    border: 1px solid var(--bs-secondary-color);
    border-radius: 5px;
    box-shadow: inset 0 0 2px #fff, 0 2px 2px -1px rgba(76, 69, 88, 0.15);
    color: inherit;
    font-size: 0.95em;
    font-weight: 500;
    padding: 0.475em 1em 0.6em;
    -webkit-text-decoration: none;
    text-decoration: none;
}
.aa-SourceFooterSeeAll:focus, .aa-SourceFooterSeeAll:hover {
    border: 1px solid rgba(62, 52, 211, 1);
    border: 1px solid rgba(var(--bs-primary-rgb), 1);
    color: rgba(62, 52, 211, 1);
    color: rgba(var(--bs-primary-rgb), 1);
}

/* ----------------*/
/* 6. Hit Layout*/
/* ----------------*/
.aa-Item {
    align-items: center;
    border-radius: 3px;
    cursor: pointer;
    display: grid;
    min-height: calc((16 * 1 * 1px) * 2.5);
    min-height: calc(calc(16 * 1 * 1px) * 2.5);
    min-height: calc(var(--aa-spacing) * 2.5);
    padding: calc(((16 * 1 * 1px) / 2) / 2);
    padding: calc(calc(calc(16 * 1 * 1px) / 2) / 2);
    padding: calc(var(--aa-spacing-half) / 2);
    /* When the result is active*/
    /* The result type icon inlined SVG or image*/
    /* wrap hit with url but we don't need to see it*/
    /* Secondary click actions*/
}
.aa-Item[aria-selected=true] {
    background-color: var(--bs-tertiary-bg);
}
.aa-Item[aria-selected=true] .aa-ItemActionButton,
.aa-Item[aria-selected=true] .aa-ActiveOnly {
    visibility: visible;
}
.aa-ItemIcon {
    align-items: center;
    background: var(--bs-body-bg);
    border-radius: 3px;
    box-shadow: inset 0 0 0 1px rgba(128, 126, 163, 0.3);
    box-shadow: inset 0 0 0 1px rgba(var(--aa-panel-border-color-rgb), var(--aa-panel-border-color-alpha));
    color: rgba(119, 119, 163, 1);
    color: rgba(var(--aa-icon-color-rgb), var(--aa-icon-color-alpha));
    display: flex;
    flex-shrink: 0;
    font-size: 0.7em;
    height: calc(20px + ((16 * 1 * 1px) / 2));
    height: calc(20px + calc(calc(16 * 1 * 1px) / 2));
    height: calc(var(--aa-icon-size) + var(--aa-spacing-half));
    justify-content: center;
    overflow: hidden;
    stroke-width: 1.6;
    stroke-width: var(--aa-icon-stroke-width);
    text-align: center;
    width: calc(20px + ((16 * 1 * 1px) / 2));
    width: calc(20px + calc(calc(16 * 1 * 1px) / 2));
    width: calc(var(--aa-icon-size) + var(--aa-spacing-half));
}
.aa-ItemIcon img {
    height: auto;
    max-height: calc(20px + ((16 * 1 * 1px) / 2) - 8px);
    max-height: calc(20px + calc(calc(16 * 1 * 1px) / 2) - 8px);
    max-height: calc(var(--aa-icon-size) + var(--aa-spacing-half) - 8px);
    max-width: calc(20px + ((16 * 1 * 1px) / 2) - 8px);
    max-width: calc(20px + calc(calc(16 * 1 * 1px) / 2) - 8px);
    max-width: calc(var(--aa-icon-size) + var(--aa-spacing-half) - 8px);
    width: auto;
}
.aa-ItemIcon svg {
    height: 20px;
    height: var(--aa-icon-size);
    width: 20px;
    width: var(--aa-icon-size);
}
.aa-ItemIcon--alignTop {
    align-self: flex-start;
}

.aa-ItemIcon--noBorder {
    background: none;
    box-shadow: none;
}

.aa-ItemIcon--picture {
    height: 96px;
    width: 96px;
}
.aa-ItemIcon--picture img {
    max-height: 100%;
    max-width: 100%;
    padding: calc((16 * 1 * 1px) / 2);
    padding: calc(calc(16 * 1 * 1px) / 2);
    padding: var(--aa-spacing-half);
}

.aa-ItemContent {
    align-items: center;
    cursor: pointer;
    display: grid;
    gap: calc((16 * 1 * 1px) / 2);
    gap: calc(calc(16 * 1 * 1px) / 2);
    grid-gap: calc((16 * 1 * 1px) / 2);
    grid-gap: calc(calc(16 * 1 * 1px) / 2);
    grid-gap: var(--aa-spacing-half);
    gap: var(--aa-spacing-half);
    grid-auto-flow: column;
    line-height: 1.25em;
    overflow: hidden;
}
.aa-ItemContent:empty {
    display: none;
}
.aa-ItemContent mark {
    background: var(--bs-highlight-bg);
    color: var(--bs-body-color);
    font-style: normal;
    padding: 0;
    font-weight: 700;
    font-weight: var(--aa-font-weight-bold);
}
.aa-ItemContent--dual {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    text-align: left;
}
.aa-ItemContent--dual .aa-ItemContentTitle,
.aa-ItemContent--dual .aa-ItemContentSubtitle {
    display: block;
}

.aa-ItemContent--indented {
    padding-left: calc(20px + (16 * 1 * 1px));
    padding-left: calc(20px + calc(16 * 1 * 1px));
    padding-left: calc(var(--aa-icon-size) + var(--aa-spacing));
}

.aa-ItemContentBody {
    display: grid;
    gap: calc(((16 * 1 * 1px) / 2) / 2);
    gap: calc(calc(calc(16 * 1 * 1px) / 2) / 2);
    grid-gap: calc(((16 * 1 * 1px) / 2) / 2);
    grid-gap: calc(calc(calc(16 * 1 * 1px) / 2) / 2);
    grid-gap: calc(var(--aa-spacing-half) / 2);
    gap: calc(var(--aa-spacing-half) / 2);
}

.aa-ItemContentTitle {
    display: inline-block;
    margin: 0 0.5em 0 0;
    max-width: 100%;
    overflow: hidden;
    padding: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.aa-ItemContentSubtitle {
    font-size: 0.92em;
}
.aa-ItemContentSubtitleIcon::before {
    border-color: var(--bs-tertiary-color);
    border-style: solid;
    content: "";
    display: inline-block;
    left: 1px;
    position: relative;
    top: -3px;
}

.aa-ItemContentSubtitle--inline .aa-ItemContentSubtitleIcon::before {
    border-width: 0 0 1.5px;
    margin-left: calc((16 * 1 * 1px) / 2);
    margin-left: calc(calc(16 * 1 * 1px) / 2);
    margin-left: var(--aa-spacing-half);
    margin-right: calc(((16 * 1 * 1px) / 2) / 2);
    margin-right: calc(calc(calc(16 * 1 * 1px) / 2) / 2);
    margin-right: calc(var(--aa-spacing-half) / 2);
    width: calc(((16 * 1 * 1px) / 2) + 2px);
    width: calc(calc(calc(16 * 1 * 1px) / 2) + 2px);
    width: calc(var(--aa-spacing-half) + 2px);
}

.aa-ItemContentSubtitle--standalone {
    align-items: center;
    color: var(--bs-body-color);
    display: grid;
    gap: calc((16 * 1 * 1px) / 2);
    gap: calc(calc(16 * 1 * 1px) / 2);
    grid-gap: calc((16 * 1 * 1px) / 2);
    grid-gap: calc(calc(16 * 1 * 1px) / 2);
    grid-gap: var(--aa-spacing-half);
    gap: var(--aa-spacing-half);
    grid-auto-flow: column;
    justify-content: start;
}
.aa-ItemContentSubtitle--standalone .aa-ItemContentSubtitleIcon::before {
    border-radius: 0 0 0 3px;
    border-width: 0 0 1.5px 1.5px;
    height: calc((16 * 1 * 1px) / 2);
    height: calc(calc(16 * 1 * 1px) / 2);
    height: var(--aa-spacing-half);
    width: calc((16 * 1 * 1px) / 2);
    width: calc(calc(16 * 1 * 1px) / 2);
    width: var(--aa-spacing-half);
}

.aa-ItemContentSubtitleCategory {
    color: var(--bs-secondary-color);
    font-weight: 500;
}

.aa-ItemContentDescription {
    color: var(--bs-body-color);
    font-size: 0.85em;
    max-width: 100%;
    overflow-x: hidden;
    text-overflow: ellipsis;
}
.aa-ItemContentDescription:empty {
    display: none;
}
.aa-ItemContentDescription mark {
    background: rgba(245, 223, 77, 0.5);
    background: rgba(var(--aa-description-highlight-background-color-rgb), var(--aa-description-highlight-background-color-alpha));
    color: rgba(38, 38, 39, 1);
    color: rgba(var(--aa-text-color-rgb), var(--aa-text-color-alpha));
    font-style: normal;
    font-weight: 500;
    font-weight: var(--aa-font-weight-medium);
}

.aa-ItemContentDash {
    color: var(--bs-secondary-color);
    display: none;
    opacity: 0.4;
}

.aa-ItemContentTag {
    color: rgba(var(--bs-primary-rgb), 1.0);;
    border-radius: 3px;
    margin: 0 0.4em 0 0;
    padding: 0.08em 0.3em;
}

.aa-ItemWrapper,
.aa-ItemLink {
    align-items: center;
    color: inherit;
    display: grid;
    gap: calc(((16 * 1 * 1px) / 2) / 2);
    gap: calc(calc(calc(16 * 1 * 1px) / 2) / 2);
    grid-gap: calc(((16 * 1 * 1px) / 2) / 2);
    grid-gap: calc(calc(calc(16 * 1 * 1px) / 2) / 2);
    grid-gap: calc(var(--aa-spacing-half) / 2);
    gap: calc(var(--aa-spacing-half) / 2);
    grid-auto-flow: column;
    justify-content: space-between;
    width: 100%;
}

.aa-ItemLink {
    color: inherit;
    -webkit-text-decoration: none;
    text-decoration: none;
}

.aa-ItemActions {
    display: grid;
    grid-auto-flow: column;
    height: 100%;
    justify-self: end;
    margin: 0 calc((16 * 1 * 1px) / -3);
    margin: 0 calc(calc(16 * 1 * 1px) / -3);
    margin: 0 calc(var(--aa-spacing) / -3);
    padding: 0 2px 0 0;
}

.aa-ItemActionButton {
    align-items: center;
    background: none;
    border: 0;
    color: var(--bs-secondary-color);
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    padding: 0;
}
.aa-ItemActionButton:hover svg, .aa-ItemActionButton:focus svg {
    color: var(--bs-body-color);
}
@media (hover: none) and (pointer: coarse) {
    .aa-ItemActionButton:hover svg, .aa-ItemActionButton:focus svg {
        color: inherit;
    }
}
.aa-ItemActionButton svg {
    color: var(--bs-secondary-color);
    margin: 0;
    margin: calc(calc(16 * 1 * 1px) / 3);
    margin: calc(var(--aa-spacing) / 3);
    stroke-width: 1.6;
    stroke-width: var(--aa-icon-stroke-width);
    width: 20px;
    width: var(--aa-action-icon-size);
}

.aa-ActiveOnly {
    visibility: hidden;
}

/*----------------*/
/* 7. Panel Header*/
/*----------------*/
.aa-PanelHeader {
    align-items: center;
    background: var(--bs-primary-bg-subtle);
    color: #fff;
    display: grid;
    height: var(--aa-modal-header-height);
    margin: 0;
    padding: calc((16 * 1 * 1px) / 2) calc(16 * 1 * 1px);
    padding: calc(calc(16 * 1 * 1px) / 2) calc(16 * 1 * 1px);
    padding: var(--aa-spacing-half) var(--aa-spacing);
    position: relative;
}
.aa-PanelHeader::after {
    background-image: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    background-image: linear-gradient(rgba(var(--aa-background-color-rgb), 1), rgba(var(--aa-background-color-rgb), 0));
    bottom: calc(((16 * 1 * 1px) / 2) * -1);
    bottom: calc(calc(calc(16 * 1 * 1px) / 2) * -1);
    bottom: calc(var(--aa-spacing-half) * -1);
    content: "";
    height: calc((16 * 1 * 1px) / 2);
    height: calc(calc(16 * 1 * 1px) / 2);
    height: var(--aa-spacing-half);
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    z-index: 9999;
    z-index: var(--aa-base-z-index);
}

/*----------------*/
/* 8. Panel Footer*/
/*----------------*/
.aa-PanelFooter {
    background-color: var(--bs-body-bg);
    box-shadow: inset 0 1px 0 var(--bs-dropdown-border-color);
    display: flex;
    justify-content: space-between;
    margin: 0;
    padding: calc(16 * 1 * 1px);
    padding: var(--aa-spacing);
    position: relative;
    z-index: 9999;
    z-index: var(--aa-base-z-index);
}
.aa-PanelFooter::after {
    background-image: linear-gradient(rgba(255, 255, 255, 0), rgba(128, 126, 163, 0.6));
    background-image: linear-gradient(rgba(var(--aa-background-color-rgb), 0), rgba(var(--aa-muted-color-rgb), var(--aa-muted-color-alpha)));
    content: "";
    height: calc(16 * 1 * 1px);
    height: var(--aa-spacing);
    left: 0;
    opacity: 0.12;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: calc((16 * 1 * 1px) * -1);
    top: calc(calc(16 * 1 * 1px) * -1);
    top: calc(var(--aa-spacing) * -1);
    z-index: calc(9999 - 1);
    z-index: calc(var(--aa-base-z-index) - 1);
}

/*----------------*/
/* 9. Detached Mode*/
/*----------------*/
.aa-DetachedContainer {
    background: var(--bs-body-bg);
    bottom: 0;
    box-shadow: 0 0 0 1px rgba(35, 38, 59, 0.1),
    0 6px 16px -4px rgba(35, 38, 59, 0.15);
    box-shadow: var(--aa-panel-shadow);
    display: flex;
    flex-direction: column;
    left: 0;
    margin: 0;
    overflow: hidden;
    padding: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 9999;
    z-index: var(--aa-base-z-index);
}
.aa-DetachedContainer::after {
    height: 32px;
}
.aa-DetachedContainer .aa-SourceHeader {
    margin: calc((16 * 1 * 1px) / 2) 0 calc((16 * 1 * 1px) / 2) 2px;
    margin: calc(calc(16 * 1 * 1px) / 2) 0 calc(calc(16 * 1 * 1px) / 2) 2px;
    margin: var(--aa-spacing-half) 0 var(--aa-spacing-half) 2px;
}
.aa-DetachedContainer .aa-Panel {
    background-color: var(--bs-body-bg);
    border-radius: 0;
    box-shadow: none;
    flex-grow: 1;
    margin: 0;
    padding: 0;
    position: relative;
}
.aa-DetachedContainer .aa-PanelLayout {
    bottom: 0;
    box-shadow: none;
    left: 0;
    margin: 0;
    max-height: none;
    overflow-y: auto;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
}
.aa-DetachedFormContainer {
    border-bottom: solid 1px rgba(128, 126, 163, 0.3);
    border-bottom: solid 1px rgba(var(--aa-panel-border-color-rgb), var(--aa-panel-border-color-alpha));
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 0;
    padding: calc((16 * 1 * 1px) / 2);
    padding: calc(calc(16 * 1 * 1px) / 2);
    padding: var(--aa-spacing-half);
}
.aa-DetachedCancelButton {
    background: none;
    border: 0;
    border-radius: 3px;
    color: var(--bs-body-color);
    cursor: pointer;
    font: inherit;
    margin: 0 0 0 calc((16 * 1 * 1px) / 2);
    margin: 0 0 0 calc(calc(16 * 1 * 1px) / 2);
    margin: 0 0 0 var(--aa-spacing-half);
    padding: 0 calc((16 * 1 * 1px) / 2);
    padding: 0 calc(calc(16 * 1 * 1px) / 2);
    padding: 0 var(--aa-spacing-half);
}
.aa-DetachedCancelButton:hover, .aa-DetachedCancelButton:focus {
    box-shadow: inset 0 0 0 1px rgba(128, 126, 163, 0.3);
    box-shadow: inset 0 0 0 1px rgba(var(--aa-panel-border-color-rgb), var(--aa-panel-border-color-alpha));
}

.aa-DetachedContainer--modal {
    border-radius: 6px;
    bottom: inherit;
    height: auto;
    margin: 0 auto;
    max-width: 680px;
    max-width: var(--aa-detached-modal-max-width);
    position: absolute;
    top: 3%;
}
.aa-DetachedContainer--modal .aa-PanelLayout {
    max-height: 500px;
    max-height: var(--aa-detached-modal-max-height);
    padding-bottom: calc((16 * 1 * 1px) / 2);
    padding-bottom: calc(calc(16 * 1 * 1px) / 2);
    padding-bottom: var(--aa-spacing-half);
    position: static;
}
.aa-DetachedContainer--modal .aa-PanelLayout:empty {
    display: none;
}

/* Search Button*/
.aa-DetachedSearchButton {
    align-items: center;
    background-color: var(--bs-body-bg);
    border: 1px solid var(--bs-secondary-border-subtle);
    border-radius: 3px;
    color: var(--bs-secondary-color);
    cursor: pointer;
    display: flex;
    font: inherit;
    font-family: inherit;
    font-family: var(--aa-font-family);
    font-size: calc(16 * 1px);
    font-size: var(--aa-font-size);
    height: 44px;
    height: var(--aa-search-input-height);
    margin: 0;
    padding: 0 calc(44px / 8);
    padding: 0 calc(var(--aa-search-input-height) / 8);
    position: relative;
    text-align: left;
    width: 100%;
}
.aa-DetachedSearchButton:focus {
    border-color: var(--bs-primary-border-subtle);
    box-shadow: rgba(62, 52, 211, 0.2) 0 0 0 3px, inset rgba(62, 52, 211, 0.2) 0 0 0 2px;
    box-shadow: var(--bs-primary-border-subtle) 0 0 0 3px, inset var(--bs-primary-border-subtle) 0 0 0 2px;
    outline: currentColor none medium;
}
.aa-DetachedSearchButtonIcon {
    align-items: center;
    color: rgba(var(--bs-primary-rgb), 1.0);
    cursor: auto;
    cursor: initial;
    display: flex;
    flex-shrink: 0;
    height: 100%;
    justify-content: center;
    width: calc(20px + (16 * 1 * 1px));
    width: calc(20px + calc(16 * 1 * 1px));
    width: calc(var(--aa-icon-size) + var(--aa-spacing));
}

.aa-DetachedSearchButtonQuery {
    color: var(--bs-body-color);
    line-height: 1.25em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.aa-DetachedSearchButtonPlaceholder[hidden] {
    display: none;
}

/* Remove scroll on `body`*/
.aa-Detached {
    height: 100vh;
    overflow: hidden;
}

.aa-DetachedOverlay {
    background-color: rgba(115, 114, 129, 0.4);
    background-color: rgba(var(--aa-overlay-color-rgb), var(--aa-overlay-color-alpha));
    height: 100vh;
    left: 0;
    margin: 0;
    padding: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: calc(9999 - 1);
    z-index: calc(var(--aa-base-z-index) - 1);
}

/*----------------*/
/* 10. Gradients*/
/*----------------*/
.aa-GradientTop,
.aa-GradientBottom {
    height: calc((16 * 1 * 1px) / 2);
    height: calc(calc(16 * 1 * 1px) / 2);
    height: var(--aa-spacing-half);
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    z-index: 9999;
    z-index: var(--aa-base-z-index);
}

.aa-GradientTop {
    background-image: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    background-image: linear-gradient(rgba(var(--aa-background-color-rgb), 1), rgba(var(--aa-background-color-rgb), 0));
    top: 0;
}

.aa-GradientBottom {
    background-image: linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
    background-image: linear-gradient(rgba(var(--aa-background-color-rgb), 0), rgba(var(--aa-background-color-rgb), 1));
    border-bottom-left-radius: calc((16 * 1 * 1px) / 4);
    border-bottom-left-radius: calc(calc(16 * 1 * 1px) / 4);
    border-bottom-left-radius: calc(var(--aa-spacing) / 4);
    border-bottom-right-radius: calc((16 * 1 * 1px) / 4);
    border-bottom-right-radius: calc(calc(16 * 1 * 1px) / 4);
    border-bottom-right-radius: calc(var(--aa-spacing) / 4);
    bottom: 0;
}

/*----------------*/
/* 11. Utilities*/
/*----------------*/
@media (hover: none) and (pointer: coarse) {
    .aa-DesktopOnly {
        display: none;
    }
}

@media (hover: hover) {
    .aa-TouchOnly {
        display: none;
    }
}