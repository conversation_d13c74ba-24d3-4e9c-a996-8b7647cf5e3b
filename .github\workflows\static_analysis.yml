name: Static analysis

on:
  push:
    branches:
      - '*'
      - "!l10n_*" # Dont test localization branches
  pull_request:
    branches:
      - '*'
      - "!l10n_*"

jobs:
  phpstan:
    name: Static analysis
    runs-on: ubuntu-22.04

    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        coverage: none
        ini-values: xdebug.max_nesting_level=1000
        extensions: mbstring, intl, gd, xsl, gmp, bcmath, :php-psr

    - name: Get Composer Cache Directory
      id: composer-cache
      run: |
        echo "::set-output name=dir::$(composer config cache-files-dir)"
    
    - uses: actions/cache@v4
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-composer-  

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Lint config files
      run: ./bin/console lint:yaml config --parse-tags
    
    - name: Lint twig templates
      run: ./bin/console lint:twig templates --env=prod

    # This causes problems with emtpy language files
    #- name: Lint translations
    #  run: ./bin/console lint:xliff translations

    - name: Check dependencies for security
      uses: symfonycorp/security-checker-action@v5
    
    - name: Check doctrine mapping
      run: ./bin/console doctrine:schema:validate --skip-sync -vvv --no-interaction

    # Use the -d option to raise the max nesting level
    - name: Generate dev container
      run: php -d xdebug.max_nesting_level=1000 ./bin/console cache:clear --env dev
    
    - name: Run PHPstan
      run: composer phpstan
