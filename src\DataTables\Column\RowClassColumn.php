<?php

declare(strict_types=1);

/*
 * This file is part of Part-DB (https://github.com/Part-DB/Part-DB-symfony).
 *
 *  Copyright (C) 2019 - 2022 <PERSON> (https://github.com/jbtronics)
 *
 *  This program is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Affero General Public License as published
 *  by the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Affero General Public License for more details.
 *
 *  You should have received a copy of the GNU Affero General Public License
 *  along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
namespace App\DataTables\Column;

use Omines\DataTablesBundle\Column\AbstractColumn;
use Omines\DataTablesBundle\DataTable;
use Symfony\Component\OptionsResolver\OptionsResolver;

class RowClassColumn extends AbstractColumn
{
    public function configureOptions(OptionsResolver $resolver): static
    {
        parent::configureOptions($resolver);

        $resolver->setDefaults([
            'label' => '',
            'orderable' => false,
            'searchable' => false,
            'className' => 'no-colvis',
            'visible' => false,
        ]);

        return $this;
    }

    public function initialize(string $name, int $index, array $options, DataTable $dataTable): void
    {
        //The field name is always "$$rowClass" as this is the name the frontend controller expects
        parent::initialize('$$rowClass', $index, $options, $dataTable); // TODO: Change the autogenerated stub
    }

    /**
     * @return mixed
     */
    public function normalize($value): mixed
    {
        return $value;
    }
}
