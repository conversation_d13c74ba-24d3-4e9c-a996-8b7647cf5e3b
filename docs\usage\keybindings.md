---
title: Keybindings
layout: default
parent: Usage
---

# Keybindings

This page lists all the keybindings of Part-DB. Currently, there are only the special character keybindings.

## Special characters

Using the keybindings below (Alt + key) you can insert special characters into the text fields of Part-DB. This works on
all text and search fields in Part-DB.

### Greek letters

| Key                 | Character             |
|---------------------|-----------------------|
| **Alt + a**         | α (Alpha)             |
| **Alt + Shift + A** | Α (Alpha uppercase)   |
| **Alt + b**         | β (Beta)              |
| **Alt + Shift + B** | Β (Beta uppercase)    |
| **Alt + g**         | γ (Gamma)             |
| **Alt + Shift + G** | Γ (Gamma uppercase)   |
| **Alt + d**         | δ (Delta)             | 
| **Alt + Shift + D** | Δ (Delta uppercase)   |
| **Alt + e**         | ε (Epsilon)           |
| **Alt + Shift + E** | Ε (Epsilon uppercase) |
| **Alt + z**         | ζ (Zeta)              |
| **Alt + Shift + Z** | Ζ (Zeta uppercase)    |
| **Alt + h**         | η (Eta)               |
| **Alt + Shift + H** | Η (Eta uppercase)     |
| **Alt + q**         | θ (Theta)             |
| **Alt + Shift + Q** | Θ (Theta uppercase)   |
| **Alt + i**         | ι (Iota)              |
| **Alt + Shift + I** | Ι (Iota uppercase)    |
| **Alt + k**         | κ (Kappa)             |
| **Alt + Shift + K** | Κ (Kappa uppercase)   |
| **Alt + l**         | λ (Lambda)            |
| **Alt + Shift + L** | Λ (Lambda uppercase)  |
| **Alt + m**         | μ (Mu)                |
| **Alt + Shift + M** | Μ (Mu uppercase)      |
| **Alt + n**         | ν (Nu)                |
| **Alt + Shift + N** | Ν (Nu uppercase)      |
| **Alt + x**         | ξ (Xi)                |
| **Alt + Shift + x** | Ξ (Xi uppercase)      |
| **Alt + o**         | ο (Omicron)           |
| **Alt + Shift + O** | Ο (Omicron uppercase) |
| **Alt + p**         | π (Pi)                |
| **Alt + Shift + P** | Π (Pi uppercase)      |
| **Alt + r**         | ρ (Rho)               |
| **Alt + Shift + R** | Ρ (Rho uppercase)     |
| **Alt + s**         | σ (Sigma)             |
| **Alt + Shift + S** | Σ (Sigma uppercase)   |
| **Alt + t**         | τ (Tau)               |
| **Alt + Shift + T** | Τ (Tau uppercase)     |
| **Alt + u**         | υ (Upsilon)           |
| **Alt + Shift + U** | Υ (Upsilon uppercase) |
| **Alt + f**         | φ (Phi)               |
| **Alt + Shift + F** | Φ (Phi uppercase)     |
| **Alt + y**         | ψ (Psi)               |
| **Alt + Shift + Y** | Ψ (Psi uppercase)     |
| **Alt + c**         | χ (Chi)               |
| **Alt + Shift + C** | Χ (Chi uppercase)     |
| **Alt + w**         | ω (Omega)             |
| **Alt + Shift + W** | Ω (Omega uppercase)   |

### Mathematical symbols

| Key                 | Character                   |
|---------------------|-----------------------------|
| **Alt + 1**         | ∑ (Sum symbol)              |
| **Alt + Shift + 1** | ∏ (Product symbol)          |
| **Alt + 2**         | ∫ (Integral symbol)         |
| **Alt + Shift + 2** | ∂ (Partial derivation)      |
| **Alt + 3**         | ≤ (Less or equal symbol)    |
| **Alt + Shift + 3** | ≥ (Greater or equal symbol) |
| **Alt + 4**         | ∞ (Infinity symbol)         |
| **Alt + Shift + 4** | ∅ (Empty set symbol)        |
| **Alt + 5**         | ≈ (Approximately)           |
| **Alt + Shift + 5** | ≠ (Not equal symbol)        |
| **Alt + 6**         | ∈ (Element of)              |
| **Alt + Shift + 6** | ∉ (Not element of)          |
| **Alt + 7**         | ∨ (Logical or)              |
| **Alt + Shift + 7** | ∧ (Logical and)             |
| **Alt + 8**         | ∠ (Angle symbol)            |
| **Alt + Shift + 8** | ∝ (Proportional to)         |
| **Alt + 9**         | √ (Square root)             |
| **Alt + Shift + 9** | ∛ (Cube root)               |
| **Alt + 0**         | ± (Plus minus)              |
| **Alt + Shift + 0** | ∓ (Minus plus)              |

### Currency symbols

Please note, the following keybindings are bound to a specific keycode. The key character is not the same on all
keyboards.
It is given here for a US keyboard layout.

For a German keyboard layout, replace ; with ö, and ' with ä.

| Key                             | Character                  |
|---------------------------------|----------------------------|
| **Alt + ;** (code 192)          | € (Euro currency symbol)   |
| **Alt + Shift + ;** (code 192)  | £ (Pound currency symbol)  |
| **Alt + '**  (code 222)         | ¥ (Yen currency symbol)    |
| **Alt + Shift + '**  (code 222) | $ (Dollar currency symbol) |

### Others

Please note the following keybindings are bound to a specific keycode. The key character is not the same on all
keyboards.
It is given here for a US keyboard layout.

For a German keyboard layout, replace `[` with `0`, and `]` with `´`.

| Key                            | Character          |
|--------------------------------|--------------------|
| **Alt + [**  (code 219)        | © (Copyright char) |
| **Alt + Shift + [** (code 219) | ® (Registered char)  |
| **Alt + ]**  (code 221)        | ™ (Trademark char) |
| **Alt + Shift + ]** (code 221) | ° (Degree char)      |
