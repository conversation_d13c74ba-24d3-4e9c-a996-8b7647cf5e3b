version: '3.3'
services:
  partdb:
    container_name: partdb
    # By default Part-DB will be running under Port 8080, you can change it here
    ports:
      - '80:80'
    volumes:
      # By default
      - ./uploads:/var/www/html/uploads
      - ./public_media:/var/www/html/public/media
      - ./db:/var/www/html/var/db
    restart: unless-stopped
    image: jbtronics/part-db1:latest
    environment:
      # Put SQLite database in our mapped folder. You can configure some other kind of database here too.
      - DATABASE_URL=sqlite:///%kernel.project_dir%/var/db/app.db
      # In docker env logs will be redirected to stderr
      - APP_ENV=docker
      
      # Uncomment this, if you want to use the automatic database migration feature. With this you have you do not have to
      # run the doctrine:migrations:migrate commands on installation or upgrade. A database backup is written to the uploads/
      # folder (under .automigration-backup), so you can restore it, if the migration fails.
      # This feature is currently experimental, so use it at your own risk!
      # - DB_AUTOMIGRATE=true

      # You can configure Part-DB using environment variables
      # Below you can find the most essential ones predefined
      # However you can add any other environment configuration you want here
      # See .env file for all available options or https://docs.part-db.de/configuration.html
      # !!! Do not use quotes around the values, as they will be interpreted as part of the value and this will lead to errors !!!

      # The language to use serverwide as default (en, de, ru, etc.)
      - DEFAULT_LANG=en
      # The default timezone to use serverwide (e.g. Europe/Berlin)
      - DEFAULT_TIMEZONE=Europe/Berlin
      # The currency that is used inside the DB (and is assumed when no currency is set). This can not be changed later, so be sure to set it the currency used in your country
      - BASE_CURRENCY=EUR
      # The name of this installation. This will be shown as title in the browser and in the header of the website
      - INSTANCE_NAME=Part-DB

      # Allow users to download attachments to the server by providing an URL
      # This could be a potential security issue, as the user can retrieve any file the server has access to (via internet)
      - ALLOW_ATTACHMENT_DOWNLOADS=0
      # Use gravatars for user avatars, when user has no own avatar defined
      - USE_GRAVATAR=0

      # Override value if you want to show a given text on homepage.
      # When this is empty the content of config/banner.md is used as banner
      #- BANNER=This is a test banner<br>with a line break
    
      # If you use a reverse proxy in front of Part-DB, you must configure the trusted proxies IP addresses here (see reverse proxy documentation for more information):
      # - TRUSTED_PROXIES=*********/8,::1,10.0.0.0/8,**********/12,***********/16 
